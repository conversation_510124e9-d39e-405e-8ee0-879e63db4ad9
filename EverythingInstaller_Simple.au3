#include <MsgBoxConstants.au3>
#include <File.au3>
#include <GUIConstantsEx.au3>
#include <ProgressConstants.au3>
#include <StaticConstants.au3>

; ===============================================================================
; AutoIt脚本：Everything搜索工具静默安装器（简化版）
; 功能：静默安装网络路径上的Everything.exe，带百分比进度条
; ===============================================================================

; 配置参数
Global Const $NETWORK_PATH = "\\t-018254\临时文件中转\5555\Everything.exe"
Global Const $LOCAL_TEMP_PATH = @TempDir & "\Everything_Installer.exe"

; 进度窗口变量
Global $g_hProgressGUI = 0
Global $g_hProgressBar = 0
Global $g_hPercentLabel = 0

; 主程序
Main()

Func Main()
    ; 显示开始信息
    Local $iResult = MsgBox($MB_YESNO + $MB_ICONQUESTION, "Everything安装器", "即将开始安装Everything搜索工具，是否继续？")
    If $iResult = $IDNO Then Exit

    ; 创建进度窗口
    CreateProgressWindow()

    ; 检查网络文件
    UpdateProgress(10, "检查网络文件...")
    If Not FileExists($NETWORK_PATH) Then
        CloseProgressWindow()
        MsgBox($MB_ICONERROR, "错误", "无法找到安装文件：" & @CRLF & $NETWORK_PATH)
        Exit 1
    EndIf

    ; 复制到本地
    UpdateProgress(30, "复制安装文件...")
    If FileExists($LOCAL_TEMP_PATH) Then FileDelete($LOCAL_TEMP_PATH)

    If Not FileCopy($NETWORK_PATH, $LOCAL_TEMP_PATH) Then
        CloseProgressWindow()
        MsgBox($MB_ICONERROR, "错误", "复制安装文件失败！")
        Exit 1
    EndIf

    ; 执行静默安装
    UpdateProgress(50, "正在安装...")
    Local $iPID = Run('"' & $LOCAL_TEMP_PATH & '" /S', @TempDir, @SW_HIDE)

    If $iPID = 0 Then
        ; 尝试其他参数
        $iPID = Run('"' & $LOCAL_TEMP_PATH & '" /SILENT', @TempDir, @SW_HIDE)
    EndIf

    If $iPID > 0 Then
        ; 等待安装完成
        UpdateProgress(70, "安装进行中...")
        ProcessWaitClose($iPID, 60)

        ; 等待文件系统更新
        UpdateProgress(90, "验证安装...")
        Sleep(3000)

        ; 验证安装
        If IsEverythingInstalled() Then
            UpdateProgress(100, "安装完成！")
            Sleep(1000)
            CloseProgressWindow()
            MsgBox($MB_ICONINFORMATION, "成功", "Everything安装成功！")
        Else
            CloseProgressWindow()
            MsgBox($MB_ICONWARNING, "警告", "安装可能未完全成功，请手动检查。")
        EndIf
    Else
        CloseProgressWindow()
        MsgBox($MB_ICONERROR, "错误", "无法启动安装程序！")
    EndIf

    ; 清理临时文件
    If FileExists($LOCAL_TEMP_PATH) Then FileDelete($LOCAL_TEMP_PATH)
EndFunc

; 创建进度窗口
Func CreateProgressWindow()
    $g_hProgressGUI = GUICreate("安装进度", 350, 100, -1, -1, BitOR($WS_CAPTION, $WS_POPUP))

    ; 百分比标签
    $g_hPercentLabel = GUICtrlCreateLabel("0%", 20, 20, 310, 20, $SS_CENTER)
    GUICtrlSetFont($g_hPercentLabel, 12, 600)

    ; 进度条
    $g_hProgressBar = GUICtrlCreateProgress(20, 50, 310, 25, $PBS_SMOOTH)

    GUISetState(@SW_SHOW, $g_hProgressGUI)
EndFunc

; 更新进度
Func UpdateProgress($iPercent, $sStatus = "")
    If $g_hProgressGUI <> 0 Then
        GUICtrlSetData($g_hProgressBar, $iPercent)
        GUICtrlSetData($g_hPercentLabel, $iPercent & "% - " & $sStatus)
    EndIf
EndFunc

; 关闭进度窗口
Func CloseProgressWindow()
    If $g_hProgressGUI <> 0 Then
        GUIDelete($g_hProgressGUI)
        $g_hProgressGUI = 0
    EndIf
EndFunc

; 检查Everything是否已安装
Func IsEverythingInstalled()
    ; 检查常见安装路径
    Local $aPaths[] = [ _
        @ProgramFilesDir & "\Everything\Everything.exe", _
        @ProgramFilesDir & " (x86)\Everything\Everything.exe" _
    ]
    
    For $sPath In $aPaths
        If FileExists($sPath) Then Return True
    Next
    
    ; 检查注册表
    Local $sRegKey = "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Everything"
    If RegRead($sRegKey, "DisplayName") <> "" Then Return True
    
    $sRegKey = "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Everything"
    If RegRead($sRegKey, "DisplayName") <> "" Then Return True
    
    Return False
EndFunc
