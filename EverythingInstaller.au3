#include <GUIConstantsEx.au3>
#include <ProgressConstants.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <File.au3>
#include <MsgBoxConstants.au3>

; ===============================================================================
; AutoIt脚本：Everything搜索工具静默安装器
; 功能：静默安装网络路径上的Everything.exe，带进度显示和错误处理
; 作者：AI Assistant
; 版本：1.0
; ===============================================================================

; 全局变量定义
Global $g_sNetworkPath = "\\t-018254\临时文件中转\5555\Everything.exe"
Global $g_sLocalTempPath = @TempDir & "\Everything_Installer.exe"
Global $g_hProgressGUI = 0
Global $g_hProgressBar = 0
Global $g_hStatusLabel = 0

; 主函数
Main()

Func Main()
    ; 显示开始确认
    Local $iResult = MsgBox($MB_YESNO + $MB_ICONQUESTION, "Everything安装器", "即将开始安装Everything搜索工具，是否继续？")
    If $iResult = $IDNO Then Exit

    ; 创建进度窗口
    CreateProgressWindow()

    ; 检查网络路径可访问性
    If Not CheckNetworkPath() Then
        ShowError("无法访问网络路径：" & $g_sNetworkPath & @CRLF & "请检查网络连接和路径是否正确。")
        CleanupAndExit(1)
    EndIf

    ; 复制安装文件到本地临时目录
    If Not CopyInstallerToLocal() Then
        ShowError("无法复制安装文件到本地临时目录。")
        CleanupAndExit(1)
    EndIf

    ; 执行静默安装
    If Not PerformSilentInstall() Then
        ShowError("Everything安装失败。")
        CleanupAndExit(1)
    EndIf

    ; 验证安装结果
    If Not VerifyInstallation() Then
        ShowError("安装验证失败，Everything可能未正确安装。")
        CleanupAndExit(1)
    EndIf

    ; 安装成功
    UpdateProgress(100)
    UpdateStatus("100% - 安装完成！")
    Sleep(1000)

    CleanupAndExit(0)
    MsgBox($MB_ICONINFORMATION, "安装成功", "Everything搜索工具已成功安装！")
EndFunc

; 创建进度显示窗口
Func CreateProgressWindow()
    $g_hProgressGUI = GUICreate("Everything安装程序", 400, 120, -1, -1, $WS_CAPTION)

    ; 状态标签（显示百分比和状态）
    $g_hStatusLabel = GUICtrlCreateLabel("0% - 正在准备安装...", 20, 20, 360, 20, $SS_LEFT)
    GUICtrlSetFont($g_hStatusLabel, 9, 400, 0, "微软雅黑")

    ; 进度条
    $g_hProgressBar = GUICtrlCreateProgress(20, 50, 360, 25, $PBS_SMOOTH)

    ; 取消按钮
    Local $hCancelBtn = GUICtrlCreateButton("取消", 320, 85, 60, 25)

    GUISetState(@SW_SHOW, $g_hProgressGUI)

    ; 处理取消按钮事件
    AdlibRegister("CheckCancelButton", 100)
EndFunc

; 检查取消按钮
Func CheckCancelButton()
    Local $nMsg = GUIGetMsg()
    If $nMsg = $GUI_EVENT_CLOSE Then
        If MsgBox($MB_YESNO + $MB_ICONQUESTION, "确认", "确定要取消安装吗？") = $IDYES Then
            CleanupAndExit(1)
        EndIf
    EndIf
EndFunc

; 更新进度条
Func UpdateProgress($iPercent)
    GUICtrlSetData($g_hProgressBar, $iPercent)
EndFunc

; 更新状态文本（包含百分比）
Func UpdateStatus($sStatus)
    ; 从进度条获取当前百分比
    Local $iPercent = GUICtrlRead($g_hProgressBar)
    GUICtrlSetData($g_hStatusLabel, $iPercent & "% - " & $sStatus)
EndFunc

; 检查网络路径是否可访问
Func CheckNetworkPath()
    UpdateProgress(10)
    UpdateStatus("正在检查网络路径...")

    ; 检查文件是否存在
    If Not FileExists($g_sNetworkPath) Then
        Return False
    EndIf

    ; 检查文件大小（确保不是0字节文件）
    Local $iFileSize = FileGetSize($g_sNetworkPath)
    If $iFileSize <= 0 Then
        Return False
    EndIf

    UpdateProgress(20)
    UpdateStatus("网络路径检查完成")
    Return True
EndFunc

; 复制安装文件到本地临时目录
Func CopyInstallerToLocal()
    UpdateProgress(30)
    UpdateStatus("正在复制安装文件...")

    ; 删除可能存在的旧文件
    If FileExists($g_sLocalTempPath) Then
        FileDelete($g_sLocalTempPath)
    EndIf

    ; 复制文件
    If Not FileCopy($g_sNetworkPath, $g_sLocalTempPath, $FC_OVERWRITE) Then
        Return False
    EndIf

    ; 验证复制是否成功
    If Not FileExists($g_sLocalTempPath) Then
        Return False
    EndIf

    UpdateProgress(50)
    UpdateStatus("文件复制完成")
    Return True
EndFunc

; 执行静默安装
Func PerformSilentInstall()
    UpdateProgress(60)
    UpdateStatus("正在安装Everything...")

    ; 尝试不同的静默安装参数
    Local $aSilentParams[] = ["/S", "/SILENT", "/s", "/silent", "/VERYSILENT", "/verysilent"]

    For $i = 0 To UBound($aSilentParams) - 1
        Local $sCommand = '"' & $g_sLocalTempPath & '" ' & $aSilentParams[$i]
        Local $iPID = Run($sCommand, @TempDir, @SW_HIDE)

        If $iPID > 0 Then
            ; 等待安装完成，最多等待60秒
            Local $iTimeout = 60
            Local $iCounter = 0

            While ProcessExists($iPID) And $iCounter < $iTimeout
                Sleep(1000)
                $iCounter += 1
                Local $iCurrentProgress = 60 + ($iCounter * 30 / $iTimeout)
                UpdateProgress($iCurrentProgress)
                UpdateStatus("安装进行中...")
            WEnd

            ; 如果进程仍在运行，强制结束
            If ProcessExists($iPID) Then
                ProcessClose($iPID)
            EndIf

            ; 检查安装是否成功
            Sleep(2000) ; 等待文件系统更新
            If VerifyInstallation() Then
                Return True
            EndIf
        EndIf
    Next

    Return False
EndFunc

; 验证安装结果
Func VerifyInstallation()
    UpdateProgress(90)
    UpdateStatus("正在验证安装...")
    
    ; 检查常见的Everything安装路径
    Local $aInstallPaths[] = [ _
        @ProgramFilesDir & "\Everything\Everything.exe", _
        @ProgramFilesDir & " (x86)\Everything\Everything.exe", _
        @AppDataDir & "\Everything\Everything.exe", _
        @LocalAppDataDir & "\Everything\Everything.exe" _
    ]
    
    For $i = 0 To UBound($aInstallPaths) - 1
        If FileExists($aInstallPaths[$i]) Then
            Return True
        EndIf
    Next
    
    ; 检查注册表中的卸载信息
    Local $sUninstallKey = "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Everything"
    Local $sUninstallKey64 = "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Everything"
    
    If RegRead($sUninstallKey, "DisplayName") <> "" Or RegRead($sUninstallKey64, "DisplayName") <> "" Then
        Return True
    EndIf
    
    Return False
EndFunc

; 显示错误信息
Func ShowError($sMessage)
    MsgBox($MB_ICONERROR, "安装错误", $sMessage)
EndFunc

; 清理临时文件并退出
Func CleanupAndExit($iExitCode)
    ; 删除临时文件
    If FileExists($g_sLocalTempPath) Then
        FileDelete($g_sLocalTempPath)
    EndIf
    
    ; 关闭进度窗口
    If $g_hProgressGUI <> 0 Then
        GUIDelete($g_hProgressGUI)
    EndIf
    
    ; 停止定时器
    AdlibUnRegister("CheckCancelButton")
    
    Exit $iExitCode
EndFunc
